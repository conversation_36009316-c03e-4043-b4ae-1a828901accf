# 离线数据转换功能代码设计文档

## 1. 整体架构设计

### 1.1 分层架构
```
Controller Layer (控制器层)
    ↓
Service (接口服务层)
    ↓
Strategy Factory (策略工厂层)
    ↓
Conversion Strategies (转换策略层)
    ↓
Data Access Layer (数据访问层)
```

### 1.2 设计模式
- **策略模式**：为每个离线表创建独立的双向转换策略
- **工厂模式**：统一管理转换策略的创建和获取
- **门面模式**：提供统一的数据转换入口

## 2. 包结构设计

### 2.1 lims-api-arch 模块（接口层）
```
com.sinoyd.lims.api.service.offline
├── OfflineDataService.java                    // 离线数据录入服务接口
├── IConversionStrategyFactory.java            // 转换策略工厂接口
├── strategy/
│   ├── IDataConversionStrategy.java           // 业务数据双向转换策略接口
│   └── IConfigDataStrategy.java               // 配置数据转换策略接口
```

### 2.2 lims-api-impl 模块（实现层）
```
com.sinoyd.lims.impl.service.offline
├── OfflineDataServiceImpl.java                // 离线数据录入服务实现类
├── ConversionStrategyFactoryImpl.java         // 策略工厂实现类
├── strategy/
│   ├── AbstractDataConversionStrategy.java    // 业务数据转换抽象基类
│   └── AbstractConfigDataStrategy.java        // 配置数据转换抽象基类
└── resolver/
    └── DataConflictResolverImpl.java          // 数据冲突解决器实现类
```

### 2.3 lims-api-public 模块（公共层）
```
com.sinoyd.lims.api.dto.vo.offLine
├── OfflineDataPackageVO.java                // 业务数据包封装类
├── OfflineConfigPackageVO.java              // 配置数据包封装类
├── OfflineDataDownloadRequestVO.java        // 业务数据下载请求
├── OfflineDataUploadRequestVO.java          // 业务数据上传请求
├── OfflineDataResponseVO.java               // 离线数据录入响应
├── OfflineDownLoadParamsVO.java             // 离线下载接口过程传参
└── OfflineCheckVO.java                      // 线数据录入数据校验类
```

## 3. 核心类设计

### 3.1 接口层类设计

#### 3.1.1 OfflineDataService
```java
/**
 * 离线数据转换服务接口
 */
public interface OfflineDataService {
    
    /**
     * 下载业务数据包
     */
    OfflineDataResponseVO downloadBusinessData(OfflineDataDownloadRequest request);
    
    /**
     * 上传业务数据包
     */
    OfflineDataResponseVO uploadBusinessData(OfflineDataUploadRequest request);
    
    /**
     * 下载配置数据包
     */
    OfflineDataResponseVO downloadConfigData(OfflineConfigDownloadRequest request);
}
```

#### 3.1.2 IConversionStrategyFactory
```java
/**
 * 转换策略工厂接口
 */
public interface IConversionStrategyFactory {
    
    /**
     * 获取业务数据转换策略
     */
    IDataConversionStrategy getDataConversionStrategy(String tableName);
    
    /**
     * 获取配置数据转换策略
     */
    IConfigDataStrategy getConfigDataStrategy(String tableName);
    
    /**
     * 注册业务数据转换策略
     */
    void registerDataConversionStrategy(String tableName, IDataConversionStrategy strategy);
    
    /**
     * 注册配置数据转换策略
     */
    void registerConfigDataStrategy(String tableName, IConfigDataStrategy strategy);
}
```

#### 3.1.3 IDataConversionStrategy
```java
/**
 * 业务数据双向转换策略接口
 */
public interface IDataConversionStrategy<T> {
    
    /**
     * 获取支持的表名
     */
    String getSupportedTableName();
    
    /**
     * 在线数据转换为离线数据（下载）
     */
    List<T> convertToOfflineData(OfflineDownLoadParamsVO paramsVO);
    
    /**
     * 离线数据转换为在线数据（上传）
     */
    void convertToOnlineData(List<T> offlineDataList);
    
    /**
     * 检测数据冲突
     */
    List<OfflineCheckVO> check(List<T> offlineDataList);
}
```

#### 3.1.4 IConfigDataStrategy
```java
/**
 * 配置数据转换策略接口
 */
public interface IConfigDataStrategy<T> {
    
    /**
     * 获取支持的表名
     */
    String getSupportedTableName();
    
    /**
     * 获取配置数据（全量下载）
     */
    List<T> getAllConfigData(String orgId);
}
```

### 3.2 实现层类设计

#### 3.2.1 OfflineDataServiceImpl
```java
/**
 * 离线数据转换服务实现类
 */
@Service
public class OfflineDataServiceImpl implements IOfflineDataService {
    
    private IConversionStrategyFactory strategyFactory;
    private IDataConflictResolver conflictResolver;
    
    /**
     * 下载业务数据包实现
     */
    public OfflineDataResponseVO downloadBusinessData(OfflineDataDownloadRequest request);
    
    /**
     * 下载配置数据包实现
     */
    public OfflineDataResponseVO downloadConfigData(OfflineConfigDownloadRequest request);
    
    /**
     * 上传业务数据包实现
     */
    public OfflineDataResponseVO uploadBusinessData(OfflineDataUploadRequest request);
  
}
```

#### 3.2.2 ConversionStrategyFactoryImpl
```java
/**
 * 转换策略工厂实现类
 */
@Component
public class ConversionStrategyFactoryImpl implements IConversionStrategyFactory {
    
    private Map<String, IDataConversionStrategy> dataStrategies;
    private Map<String, IConfigDataStrategy> configStrategies;
    
    /**
     * 策略注册和获取方法实现（使用枚举进行管理）
     */
}
```

#### 3.2.3 AbstractDataConversionStrategy
```java
/**
 * 业务数据转换抽象基类
 */
public abstract class AbstractDataConversionStrategy<T> implements IDataConversionStrategy<T> {
    
    /**
     * 通用的数据转换逻辑
     */
    protected abstract T mapToOfflineData(Object onlineData);
    
    /**
     * 通用的数据验证逻辑
     */
    protected abstract boolean validateOfflineData(T offlineData);
    
    /**
     * 通用的冲突检测逻辑
     */
    protected abstract ConflictInfo checkDataConflict(T offlineData, Object onlineData);
}
```

#### 3.2.4 AbstractConfigDataStrategy
```java
/**
 * 配置数据转换抽象基类
 */
public abstract class AbstractConfigDataStrategy<T> implements IConfigDataStrategy<T> {
    
    /**
     * 通用的配置数据查询逻辑
     */
    protected abstract List<Object> queryConfigData(String orgId);
    
    /**
     * 通用的数据映射逻辑
     */
    protected abstract T mapToConfigData(Object sourceData);
}
```

### 3.3 公共层类设计

#### 3.3.1 OfflineDataPackage
```java
/**
 * 离线业务数据包封装类VO
 */
@Data
public class OfflineDataPackageVO {
    
    private String receiveId;                           // 采样单ID
    private String packageVersion;                      // 数据包版本
    private Date createTime;                           // 创建时间
    private String orgId;                              // 所属机构
    
    // 11个业务表数据
    private List<OffLineSampleRecordVO> sampleRecords;
    private List<OffLineSampleFolderVO> sampleFolders;
    private List<OffLineFolderSignVO> folderSigns;
    private List<OffLineSampleItemListVO> sampleItems;
    private List<OffLineAnalyseDataVO> analyseData;
    private List<OffLineSampleGroupVO> sampleGroups;
    private List<OffLineSampleGroupTestVO> sampleGroupTests;
    private List<OffLinePublicParamsDataVO> publicParams;
    private List<OffLineFolderParamsDataVO> folderParams;
    private List<OffLineSampleParamsDataVO> sampleParams;
    private List<OffLineDocumentVO> documents;
}
```

#### 3.3.2 OfflineConfigPackage
```java
/**
 * 离线配置数据包封装类VO
 */
@Data
public class OfflineConfigPackageVO {
    
    private String packageVersion;                      // 数据包版本
    private Date createTime;                           // 创建时间
    private String orgId;                              // 所属机构
    
    // 4个配置表数据
    private List<OffLineSampleGroupTypeVO> sampleGroupTypes;
    private List<OffLineSampleGroupTypeTestVO> sampleGroupTypeTests;
    private List<OffLineSampleTypeVO> sampleTypes;
    private List<OffLinePersonVO> persons;
}
```

#### 3.3.3 请求响应类设计
```java
/**
 * 业务数据下载请求VO
 */
@Data
public class OfflineDataDownloadRequestVO {
    private String orgCode; 							// 所属机构编码
    private String orgId;                           // 所属机构
    private Collection<String> receiveIds;                           // 需要下载的采样单id集合
}

/**
 * 业务数据上传请求VO
 */
@Data
public class OfflineDataUploadRequestVO {
    private Collection<String> receiveIds;           // 选择需要上传同步的采样单id集合
    private String orgCode; 							// 所属机构编码
    private String orgId;                           // 所属机构
}

/**
 * 离线数据录入响应VO
 */
@Data
public class OfflineDataResponseVO {
    private String orgCode;						// 所属机构编码
    private String orgId;                           // 所属机构
    
    private Collection<OfflineDataPackageVO> dataPackage;           // 业务数据包集合
    private Collection<OfflineConfigPackageVO> dataPackage;           // 配置数据包集合
}


```

#### 3.3.4 下载接口过程传参VO(OfflineDownLoadParamsVO)

```java
import java.util.Collection;

/**
 * 下载接口过程传参VO
 */
@Data
public class OfflineDownLoadParamsVO {

    /**
     * 采样单id集合
     */
    private Collection<String> receiveIds;

    /**
     * 采样单集合
     */
    private Collection<DtoReceiveSampleRecord> receiveSampleRecords;

    /**
     * 点位id集合
     */
    private Collection<String> folderIds;

    /**
     * 点位集合
     */
    private Collection<DtoSampleFolder> sampleFolders;

    /**
     * 样品id
     */
    private Collection<String> sampleIds;

    /**
     * 样品集合
     */
    private Collection<DtoSample> samples;

}
```

#### 3.3.5 离线数据录入数据校验VO(OfflineCheckVO)
```java
import java.util.Collection;

/**
 * 离线数据录入数据校验VO
 */
@Data
public class OfflineCheckVO {

    /**
     * 校验的采样单编号
     */
    private String receiveCode;

    /**
     * 是否校验通过
     */
    private Boolean isPass;

}
```


## 4. 数据流转设计

### 4.1 下载流程
1. Controller接收下载请求
2. Facade调用策略工厂获取转换策略
3. 各策略执行在线数据→离线数据转换
4. 封装为OfflineDataPackage返回

### 4.2 上传流程
1. Controller接收上传请求
2. Facade调用冲突解决器检测冲突
3. 各策略执行离线数据→在线数据转换
4. 返回ConversionResult结果

### 4.3 配置数据下载流程
1. Controller接收配置下载请求
2. Facade调用配置策略获取全量数据
3. 封装为OfflineConfigPackage返回

## 5. 扩展性设计

### 5.1 新增离线表支持
1. 创建对应的VO类
2. 实现IDataConversionStrategy接口
3. 在工厂中注册新策略

### 5.2 新增配置表支持
1. 创建对应的VO类
2. 实现IConfigDataStrategy接口
3. 在工厂中注册新策略

### 5.3 新增转换规则
1. 继承AbstractDataConversionStrategy
2. 实现具体的转换逻辑
3. 注册到策略工厂

## 6. 异常处理设计

### 6.1 异常类层次
- OfflineDataConversionException：转换异常基类
- DataConflictException：数据冲突异常
- ValidationException：数据验证异常

### 6.2 异常处理策略
- 转换失败时回滚事务
- 记录详细的错误日志
- 返回友好的错误信息

---

**文档版本**: V1.0  
**创建日期**: 2025/09/23  
**创建人**: 开发团队  
**适用阶段**: 阶段一、阶段二
